<?php

/**
 * @befie MisResProd rpc
 * @file MisResProdRpc.php
 * <AUTHOR>
 * @version 1.0
 * @date 2024-03-07
 */

class Service_Data_Rpc_MisResProdRpc
{
    const SERVICE_NAME = 'misresprod'; # misresprod service标识
    const COOKIE_KEY = 'ZYBIPSCAS';

    /**
     * 调用方法统一收敛
     * @param string $method
     * @param array $arguments
     * @return false|mixed
     */
    public function __call($method, array $arguments)
    {
        return self::callService("/misresprod/service/".$method, $arguments[0]);
    }

    /**
     * 请求resourceproducer服务
     * @param $uri
     * @param $params
     * @param $attachHeaders
     * @return bool|array
     */
    public static function callService($uri, $params, $attachHeaders = [])
    {
        $header = [
            'Content-Type' => 'application/json',
        ];
        if (!empty($attachHeaders)) {
            $header = array_merge($header, $attachHeaders);
        }

        $result = Hk_Util_Http::post(
            $uri,
            self::SERVICE_NAME,
            $params,
            $header
        );

        if ($result === false || !isset($result["errNo"])
            || !isset($result["data"]) || $result["errNo"] != 0) {

            $logContext = [
                'uri' => $uri,
                'params' => $params,
            ];
            Bd_Log::warning("request misresprod failed: " . json_encode($logContext));
            return false;
        }

        return $result["data"];
    }

    /**
     * 获取session
     * @return array|bool
     */
    public static function getSessionFromAuth()
    {
        if (empty($_COOKIE) || empty($_COOKIE[self::COOKIE_KEY])) {
            Bd_Log::addNotice('get_session_cookie empty', 1);
            return false;
        }

        $ret = self::callService(
            "/misresprod/auth/getsession",
            ['a' => 'b'], // 随便写的,保证请求body有值,否则接口调不通
            [
                'Cookie' => sprintf('%s=%s', self::COOKIE_KEY, $_COOKIE[self::COOKIE_KEY]),
            ]);
        if ($ret === false) {
            Bd_Log::addNotice('getIpsAccTokenErr', 1);
            $ret = [];
        }

        return $ret['userInfo']??[];
    }

    /**
     * 校验api权限
     * @param $uri
     * @return array|bool
     */
    public static function checkApiFromAuth($uri)
    {
        if (empty($_COOKIE) || empty($_COOKIE[self::COOKIE_KEY])) {
            Bd_Log::addNotice('get_session_cookie empty', 1);
            return false;
        }

        return self::callService(
            "/misresprod/auth/checkapi",
            ['requestURI' => $uri],
            [
                'Cookie' => sprintf('%s=%s', self::COOKIE_KEY, $_COOKIE[self::COOKIE_KEY]),
            ]
        );
    }

    /**
     * 图片翻译接口
     * @param string $image 图片数据
     * @param string $transFrom 源语言
     * @param string $transTo 目标语言
     * @return array|bool
     */
    public static function pictranslate($image, $transFrom, $transTo)
    {
        
        if (empty($image) || empty($transFrom) || empty($transTo)) {
            Bd_Log::addNotice('pictranslate params empty', 1);
            return false;
        }

        $params = [
            'image' => $image,
            'transFrom' => $transFrom,
            'transTo' => $transTo,
        ];
        
        return self::callService("/misresprod/service/pictranslate", $params);
    }
}
