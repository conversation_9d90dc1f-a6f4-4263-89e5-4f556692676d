<?php
/**
 * @brief   反馈图书问题重新生产
 * @file    FeatureReportReproduce.php
 * <AUTHOR>
 * @version 1.0
 * @date    2024-11-11
 */
class Service_Page_Report_V1_FeatureReportReproduce
{
    private $objDSFeatureReport;

    public function __construct()
    {
        $this->objDSFeatureReport = new Service_Data_Report_FeatureReport();
    }

    public function execute($arrInput)
    {   

        // 1. 远程调用misresprodtranslate的rpc
        // 从URL中提取文件名部分（不使用正则）
        $imageUrl = $arrInput['bookPagePid'];
        $fileName = basename($imageUrl); 
        $imageName = pathinfo($fileName, PATHINFO_FILENAME); 

        $ret = Service_Data_Rpc_MisResProdRpc::pictranslate($imageName, "en", "zh");

        // 检查远程调用是否成功（callService内部已处理网络错误、errNo检查等）
        if ($ret === false) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SYSTEM_CRAZY, '图片翻译服务调用失败');
        }

        // 提取relatedBook/picture/pid
        $relatedBookPicturePid = '';
        if (isset($ret['relatedBook']['picture']['pid']) && !empty($ret['relatedBook']['picture']['pid'])) {
            $relatedBookPicturePid = $ret['relatedBook']['picture']['pid'];
        } else {
            // 如果relatedBook的picture pid为空，直接返回错误
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SYSTEM_CRAZY, '未找到相关书籍信息');
        }

        // 2. 通过bookPagePid查询信息
        $info = $this->queryBookInfoByPid($relatedBookPicturePid);
        if (empty($info)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, '图片Pid错误');
        }

        // 3. 创建生产流程
        return $this->createTranslateProduceProcess($arrInput['uid'], $info);
        

    }

    private function queryBookInfoByPid($pid)
    {
        $info = Service_Data_Kdresource_Translate::QueryTranslateBookInfoByPid($pid);
        if (empty($info)) {
            return [];
        }
        return $info;
    }

    private function createTranslateProduceProcess($uid, $info)
    {
        $resourceIns = [
            'thirdId'   => $info['bookKey'],
            'createUid' => $uid,
            'isbn' => strval($info['code']),
            'name' => strval($info['name']),
            'grade' => intval($info['grade']),
            'term' => intval($info['term']),
            'publisher' => strval($info['version']),
            'coverPages' => [
                strval($info['cover'])
            ],
            'model' => intval(1),
            'product' => [
                [
                    'productPid' => strval($info['pid']),
                    'order' => intval($info['order'])
                ]
            ]
        ];
        $param = [
            'flowConfId' => 113,
            'resourceIns' => $resourceIns,
            'flowCustom' => [
                'model' => strval(1),
                'sourceProcessName' => "englishbooksub-v1"
            ]
        ];
        $ret = Service_Data_ResProd_Product::createIncludeEnv($param);
        if (empty($ret)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SYSTEM_CRAZY, '创建生产流程失败');
        }
        return $ret['prodId'];
    }
}